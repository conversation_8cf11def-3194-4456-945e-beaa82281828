// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i26;
import 'package:flutter/material.dart' as _i27;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i8;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i16;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i28;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/form_page.dart'
    as _i5;
import 'package:storetrack_app/features/home/<USER>/pages/fqpd_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i6;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i9;
import 'package:storetrack_app/features/home/<USER>/pages/notes_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart'
    as _i12;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i13;
import 'package:storetrack_app/features/home/<USER>/pages/qpmd_page.dart'
    as _i14;
import 'package:storetrack_app/features/home/<USER>/pages/question_page.dart'
    as _i15;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i17;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_page.dart'
    as _i19;
import 'package:storetrack_app/features/home/<USER>/pages/store_info_page.dart'
    as _i20;
import 'package:storetrack_app/features/home/<USER>/pages/sub_header_page.dart'
    as _i21;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i22;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i23;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i24;
import 'package:storetrack_app/features/notification/presentation/pages/notification_page.dart'
    as _i11;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i18;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i25;

/// generated route for
/// [_i1.AssistantPage]
class AssistantRoute extends _i26.PageRouteInfo<void> {
  const AssistantRoute({List<_i26.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i1.AssistantPage();
    },
  );
}

/// generated route for
/// [_i2.DashboardHolderPage]
class DashboardHolderRoute extends _i26.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i26.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i2.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i3.DashboardPage]
class DashboardRoute extends _i26.PageRouteInfo<void> {
  const DashboardRoute({List<_i26.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i3.DashboardPage();
    },
  );
}

/// generated route for
/// [_i4.FQPDPage]
class FQPDRoute extends _i26.PageRouteInfo<void> {
  const FQPDRoute({List<_i26.PageRouteInfo>? children})
      : super(
          FQPDRoute.name,
          initialChildren: children,
        );

  static const String name = 'FQPDRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i4.FQPDPage();
    },
  );
}

/// generated route for
/// [_i5.FormPage]
class FormRoute extends _i26.PageRouteInfo<FormRouteArgs> {
  FormRoute({
    _i27.Key? key,
    required _i28.TaskDetail task,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          FormRoute.name,
          args: FormRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'FormRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FormRouteArgs>();
      return _i5.FormPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class FormRouteArgs {
  const FormRouteArgs({
    this.key,
    required this.task,
  });

  final _i27.Key? key;

  final _i28.TaskDetail task;

  @override
  String toString() {
    return 'FormRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i6.HomePage]
class HomeRoute extends _i26.PageRouteInfo<void> {
  const HomeRoute({List<_i26.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i6.HomePage();
    },
  );
}

/// generated route for
/// [_i7.JourneyMapPage]
class JourneyMapRoute extends _i26.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i26.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i7.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i8.LoginPage]
class LoginRoute extends _i26.PageRouteInfo<void> {
  const LoginRoute({List<_i26.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i8.LoginPage();
    },
  );
}

/// generated route for
/// [_i9.MorePage]
class MoreRoute extends _i26.PageRouteInfo<void> {
  const MoreRoute({List<_i26.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i9.MorePage();
    },
  );
}

/// generated route for
/// [_i10.NotesPage]
class NotesRoute extends _i26.PageRouteInfo<NotesRouteArgs> {
  NotesRoute({
    _i27.Key? key,
    required _i28.TaskDetail task,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          NotesRoute.name,
          args: NotesRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'NotesRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NotesRouteArgs>();
      return _i10.NotesPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class NotesRouteArgs {
  const NotesRouteArgs({
    this.key,
    required this.task,
  });

  final _i27.Key? key;

  final _i28.TaskDetail task;

  @override
  String toString() {
    return 'NotesRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i11.NotificationsPage]
class NotificationsRoute extends _i26.PageRouteInfo<void> {
  const NotificationsRoute({List<_i26.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i11.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i12.PosPage]
class PosRoute extends _i26.PageRouteInfo<PosRouteArgs> {
  PosRoute({
    _i27.Key? key,
    _i28.TaskDetail? task,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          PosRoute.name,
          args: PosRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'PosRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<PosRouteArgs>(orElse: () => const PosRouteArgs());
      return _i12.PosPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class PosRouteArgs {
  const PosRouteArgs({
    this.key,
    this.task,
  });

  final _i27.Key? key;

  final _i28.TaskDetail? task;

  @override
  String toString() {
    return 'PosRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i13.ProfilePage]
class ProfileRoute extends _i26.PageRouteInfo<void> {
  const ProfileRoute({List<_i26.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i13.ProfilePage();
    },
  );
}

/// generated route for
/// [_i14.QPMDPage]
class QPMDRoute extends _i26.PageRouteInfo<QPMDRouteArgs> {
  QPMDRoute({
    _i27.Key? key,
    _i28.Question? question,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          QPMDRoute.name,
          args: QPMDRouteArgs(
            key: key,
            question: question,
          ),
          initialChildren: children,
        );

  static const String name = 'QPMDRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<QPMDRouteArgs>(orElse: () => const QPMDRouteArgs());
      return _i14.QPMDPage(
        key: args.key,
        question: args.question,
      );
    },
  );
}

class QPMDRouteArgs {
  const QPMDRouteArgs({
    this.key,
    this.question,
  });

  final _i27.Key? key;

  final _i28.Question? question;

  @override
  String toString() {
    return 'QPMDRouteArgs{key: $key, question: $question}';
  }
}

/// generated route for
/// [_i15.QuestionPage]
class QuestionRoute extends _i26.PageRouteInfo<QuestionRouteArgs> {
  QuestionRoute({
    _i27.Key? key,
    required _i28.Form form,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          QuestionRoute.name,
          args: QuestionRouteArgs(
            key: key,
            form: form,
          ),
          initialChildren: children,
        );

  static const String name = 'QuestionRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<QuestionRouteArgs>();
      return _i15.QuestionPage(
        key: args.key,
        form: args.form,
      );
    },
  );
}

class QuestionRouteArgs {
  const QuestionRouteArgs({
    this.key,
    required this.form,
  });

  final _i27.Key? key;

  final _i28.Form form;

  @override
  String toString() {
    return 'QuestionRouteArgs{key: $key, form: $form}';
  }
}

/// generated route for
/// [_i16.ResetPasswordPage]
class ResetPasswordRoute extends _i26.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i27.Key? key,
    required String email,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i16.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i27.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i17.SchedulePage]
class ScheduleRoute extends _i26.PageRouteInfo<void> {
  const ScheduleRoute({List<_i26.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i17.SchedulePage();
    },
  );
}

/// generated route for
/// [_i18.SplashPage]
class SplashRoute extends _i26.PageRouteInfo<void> {
  const SplashRoute({List<_i26.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i18.SplashPage();
    },
  );
}

/// generated route for
/// [_i19.StoreHistoryPage]
class StoreHistoryRoute extends _i26.PageRouteInfo<StoreHistoryRouteArgs> {
  StoreHistoryRoute({
    _i27.Key? key,
    required int storeId,
    required int taskId,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          StoreHistoryRoute.name,
          args: StoreHistoryRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryRouteArgs>();
      return _i19.StoreHistoryPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryRouteArgs {
  const StoreHistoryRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i27.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i20.StoreInfoPage]
class StoreInfoRoute extends _i26.PageRouteInfo<void> {
  const StoreInfoRoute({List<_i26.PageRouteInfo>? children})
      : super(
          StoreInfoRoute.name,
          initialChildren: children,
        );

  static const String name = 'StoreInfoRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i20.StoreInfoPage();
    },
  );
}

/// generated route for
/// [_i21.SubHeaderPage]
class SubHeaderRoute extends _i26.PageRouteInfo<SubHeaderRouteArgs> {
  SubHeaderRoute({
    _i27.Key? key,
    required String title,
    required List<_i28.QuestionPart> questionParts,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          SubHeaderRoute.name,
          args: SubHeaderRouteArgs(
            key: key,
            title: title,
            questionParts: questionParts,
          ),
          initialChildren: children,
        );

  static const String name = 'SubHeaderRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SubHeaderRouteArgs>();
      return _i21.SubHeaderPage(
        key: args.key,
        title: args.title,
        questionParts: args.questionParts,
      );
    },
  );
}

class SubHeaderRouteArgs {
  const SubHeaderRouteArgs({
    this.key,
    required this.title,
    required this.questionParts,
  });

  final _i27.Key? key;

  final String title;

  final List<_i28.QuestionPart> questionParts;

  @override
  String toString() {
    return 'SubHeaderRouteArgs{key: $key, title: $title, questionParts: $questionParts}';
  }
}

/// generated route for
/// [_i22.TaskDetailsPage]
class TaskDetailsRoute extends _i26.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i27.Key? key,
    required _i28.TaskDetail task,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i22.TaskDetailsPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.task,
  });

  final _i27.Key? key;

  final _i28.TaskDetail task;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i23.TodayPage]
class TodayRoute extends _i26.PageRouteInfo<void> {
  const TodayRoute({List<_i26.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i23.TodayPage();
    },
  );
}

/// generated route for
/// [_i24.UnscheduledPage]
class UnscheduledRoute extends _i26.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i26.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      return const _i24.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i25.WebBrowserPage]
class WebBrowserRoute extends _i26.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i27.Key? key,
    required String url,
    List<_i26.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i26.PageInfo page = _i26.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i25.WebBrowserPage(
        key: args.key,
        url: args.url,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
  });

  final _i27.Key? key;

  final String url;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url}';
  }
}
