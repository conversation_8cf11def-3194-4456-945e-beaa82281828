import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/progress_item_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart'; // For generated routes

@RoutePage()
class QuestionPage extends StatefulWidget {
  final entities.Form form;

  const QuestionPage({
    super.key,
    required this.form,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // State for comment fields
  final Map<num, String?> _selectedCommentTypes = {};
  final Map<num, TextEditingController> _commentControllers = {};
  // Get questions from the form
  List<entities.Question>? get questionItems => widget.form.questions;

  @override
  void initState() {
    super.initState();
    if (widget.form.questions != null) {
      for (final question in widget.form.questions!) {
        if (question.isComment == true && question.questionId != null) {
          _commentControllers[question.questionId!] = TextEditingController();
          // Initialize with null or a default first item if applicable
          _selectedCommentTypes[question.questionId!] = null;
        }
      }
    }
  }

  @override
  void dispose() {
    _commentControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.form.formName ?? 'Questions',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            questionItems == null || questionItems!.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No questions available for this form',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: questionItems!.length,
                    itemBuilder: (context, index) {
                      final question = questionItems![index];

                      if (question.isComment == true &&
                          question.questionId != null) {
                        final questionId = question.questionId!;
                        // Ensure controller and selected value exists, though initState should handle this
                        if (!_commentControllers.containsKey(questionId)) {
                          _commentControllers[questionId] =
                              TextEditingController();
                        }
                        if (!_selectedCommentTypes.containsKey(questionId)) {
                          _selectedCommentTypes[questionId] = null;
                        }

                        final List<entities.CommentType> commentTypes =
                            question.commentTypes ?? [];

                        return Card(
                          margin: const EdgeInsets.symmetric(
                              vertical:
                                  4.0), // Consistent with ProgressItemCard if it has margin
                          elevation: 1,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  question.questionDescription ?? 'Comment',
                                  style: textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.black,
                                  ),
                                ),
                                const Gap(12),
                                if (commentTypes.isNotEmpty)
                                  DropdownButtonFormField<String>(
                                    decoration: InputDecoration(
                                      labelText: 'Comment Type',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                    ),
                                    value: _selectedCommentTypes[questionId],
                                    hint: const Text('Select one...'),
                                    isExpanded: true,
                                    items: commentTypes.map((commentType) {
                                      return DropdownMenuItem<String>(
                                        value: commentType
                                            .commentType, // Assuming commentType string is unique enough for value
                                        child: Text(commentType.commentType ??
                                            'Unnamed Type'),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) {
                                      setState(() {
                                        _selectedCommentTypes[questionId] =
                                            newValue;
                                      });
                                    },
                                    validator: (value) {
                                      if (question.isCommentMandatory == true &&
                                          value == null) {
                                        return 'Please select a comment type.';
                                      }
                                      return null;
                                    },
                                  ),
                                if (commentTypes.isNotEmpty) const Gap(12),
                                TextFormField(
                                  controller: _commentControllers[questionId],
                                  decoration: InputDecoration(
                                    labelText: 'Your comment',
                                    hintText: 'Enter your comment here...',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    filled: true,
                                    fillColor: Colors.white,
                                  ),
                                  maxLines: 4,
                                  validator: (value) {
                                    if (question.isCommentMandatory == true &&
                                        (value == null || value.isEmpty)) {
                                      return 'Comment cannot be empty.';
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      } else {
                        // Calculate progress based on measurements if available
                        double progress = 0.0;
                        String progressText = '0 of 0';

                        if (question.measurements != null &&
                            question.measurements!.isNotEmpty) {
                          int totalMeasurements = question.measurements!.length;
                          // This could be enhanced later with actual progress tracking
                          progressText = '0 of $totalMeasurements';
                        }

                        return ProgressItemCard(
                          title: question.questionDescription ??
                              'Unnamed Question',
                          progress: progress,
                          progressText: progressText,
                          width: 1.0, // Full width for list view
                          onTap: () {
                            final questionParts = question.questionParts ?? [];
                            final hasSignature = question.hasSignature ?? false;
                            final isMulti = question.isMulti ?? false;

                            if (questionParts.length != 1 || hasSignature) {
                              if (isMulti) {
                                logger('123qwe Navigating to FQPDRoute');
                                AutoRouter.of(context).push(const FQPDRoute());
                              } else {
                                logger('123qwe Navigating to SubHeaderRoute');
                                AutoRouter.of(context).push(SubHeaderRoute(
                                  title:
                                      question.questionDescription ?? 'Details',
                                  questionParts: questionParts,
                                ));
                              }
                            } else {
                              logger('123qwe Navigating to QPMDRoute');
                              AutoRouter.of(context)
                                  .push(QPMDRoute(question: question));
                            }
                          },
                        );
                      }
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
