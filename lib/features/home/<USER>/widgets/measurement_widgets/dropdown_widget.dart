import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class DropdownWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;

  const DropdownWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              measurement.measurementDescription ?? 'Select Option',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const Gap(12),
            DropdownButtonFormField<String>(
              value: value,
              decoration: InputDecoration(
                hintText: 'Select an option...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.blackTint2),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.blackTint2),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.primaryBlue),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 16.0,
                ),
              ),
              isExpanded: true,
              items: options.map((option) {
                return DropdownMenuItem<String>(
                  value: option.measurementOptionId?.toString(),
                  child: Text(
                    option.measurementOptionDescription ?? 'Unnamed Option',
                    style: textTheme.bodyMedium?.copyWith(
                      color: AppColors.black,
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
              validator: (selectedValue) {
                if (measurement.required == true && selectedValue == null) {
                  return 'Please select an option';
                }
                return null;
              },
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: AppColors.primaryBlue,
              ),
            ),
            if (measurement.required == true && value == null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'This field is required',
                  style: textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
